import fastifyJwt from "@fastify/jwt";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";

import { customResponse } from "@/helpers";
import { env } from "@/utils";

export const authMiddlewares = async (fastify: FastifyInstance) => {
  fastify.register(fastifyJwt, {
    secret: (env.APP_JWT_SECRET as string) || "secret",
    // for handle manual decorator in controller because user already used on passport
    decoratorName: "jwt",
  });
};

export const authValidation = async (
  req: FastifyRequest,
  reply: FastifyReply
) => {
  try {
    await req.jwtVerify();
  } catch (err) {
    customResponse(reply, null, 401, "Unauthorized");
  }
};
