{"name": "oreo-socola-pie-be", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node dist/index.js", "start:dev": " nodemon -r dotenv/config -r tsconfig-paths/register src/index.ts dotenv_config_path=.env.local", "start:dev:staging": " nodemon -r dotenv/config -r tsconfig-paths/register src/index.ts dotenv_config_path=.env.staging", "start:dev:prod": " nodemon -r dotenv/config -r tsconfig-paths/register src/index.ts dotenv_config_path=.env.prod", "start:prod": "npm run start", "migrate:dev": "npm run db:generate && npm run db:migrate", "migrate:prod": "npm run db:migrate:prod && npm run db:push:prod", "db:migrate:prod": "drizzle-kit migrate --config=dist/db/drizzle.config.js", "db:push:prod": "drizzle-kit push --config=dist/db/drizzle.config.js", "db:generate": "drizzle-kit generate --config=src/db/drizzle.config.ts", "db:migrate": "drizzle-kit migrate --config=src/db/drizzle.config.ts", "db:push": "drizzle-kit push --config=src/db/drizzle.config.ts", "db:drop": "drizzle-kit drop --config=src/db/drizzle.config.ts", "db:check": "drizzle-kit check --config=src/db/drizzle.config.ts", "db:studio": "drizzle-kit studio --config=src/db/drizzle.config.ts", "db:up": "drizzle-kit up --config=src/db/drizzle.config.ts", "db:seed": "ts-node -r tsconfig-paths/register src/db/seeds.ts", "build": " tsc && tsc-alias -p tsconfig.json", "ts.check": "tsc --project tsconfig.json", "format": "eslint --fix --ext .js,.ts . && prettier --write .", "check:format": "prettier --check .", "check:lint": "eslint . --ext .js,.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-mediaconvert": "^3.616.0", "@aws-sdk/client-s3": "^3.614.0", "@aws-sdk/s3-request-presigner": "^3.614.0", "@fastify/cookie": "^9.3.1", "@fastify/cors": "^8.4.0", "@fastify/helmet": "^11.1.1", "@fastify/jwt": "^7.2.1", "@fastify/multipart": "^8.3.0", "@fastify/oauth2": "^7.8.1", "@fastify/one-line-logger": "^1.3.0", "@fastify/passport": "^2.4.0", "@fastify/rate-limit": "^9.1.0", "@fastify/secure-session": "^7.5.1", "@fastify/session": "^10.9.0", "@fastify/swagger": "^8.14.0", "@fastify/swagger-ui": "^3.0.0", "axios": "^1.7.2", "bcrypt": "^5.1.1", "canvas": "^2.11.2", "dotenv": "^16.4.5", "drizzle-orm": "^0.38.2", "fastify": "^4.26.2", "fastify-plugin": "^4.5.1", "fastify-type-provider-zod": "^1.1.9", "fastify-zod": "^1.4.0", "http-errors": "^2.0.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.13", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-http-bearer": "^1.0.1", "passport-oauth2": "^1.8.0", "pg": "^8.11.5", "pino": "^8.15.3", "prettier-config-standard": "^7.0.0", "replicate": "^0.31.1", "sharp": "^0.33.4", "ts-node": "^10.9.2", "tsc-alias": "^1.8.8", "zod": "^3.23.4"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/fluent-ffmpeg": "^2.1.24", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.12.7", "@types/nodemailer": "^6.4.15", "@types/passport-facebook": "^3.0.3", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-http-bearer": "^1.0.41", "@types/pg": "^8.11.6", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "drizzle-kit": "^0.30.1", "esbuild-register": "^3.4.2", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-standard": "^5.0.0", "nodemon": "^3.0.1", "prettier": "^3.0.3", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.5"}}