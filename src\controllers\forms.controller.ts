import { FastifyReply, FastifyRequest } from "fastify";

import { customError, customResponse } from "@/helpers";
import services from "@/services/forms.service";

const controllers = {
  formsSubmit: async (
    req: FastifyRequest<{
      Body: any;
    }>,
    reply: FastifyReply
  ) => {
    try {
      const data = req.body;

      await services.submitForm(data);

      return customResponse(reply, null, 200, "Submit Form success");
    } catch (error) {
      return customError(error, reply);
    }
  },
};
export default controllers;
