import { z } from "zod";

export const usersSchemas = z.object({
  firstName: z.string().min(2).max(255),
  lastName: z.string().min(2).max(255).optional(),
  email: z.string().email(),
  zaloNumber: z.string().min(6).max(255),
});

export type UsersSchemasType = z.infer<typeof usersSchemas>;

export const usersRegisterSchema = z.object({
  firstName: z.string().min(2).max(255),
  lastName: z.string().min(2).max(255).optional(),
  email: z.string().email(),
  zaloNumber: z.string().min(6).max(255),
});
export type UsersRegisterSchemasType = z.infer<typeof usersRegisterSchema>;
export const usersRegisterSchemas = z.object({
  body: usersRegisterSchema,
});

export const usersLoginSchema = z.object({
  email: z.string().email(),
});
export type UsersLoginSchemaType = z.infer<typeof usersLoginSchema>;
export const usersLoginSchemas = z.object({
  body: usersLoginSchema,
});
