## Getting Started

To get started with the project, follow these steps:

1. Clone the repository:

   ```
   git clone <repository_url>
   ```

2. Install dependencies:

   ```
   npm install

   ```

3. Set up environment variables:

Create a `.env` file in the root directory and define the following variables from `.env.example`

4. Run database generate :

   ```
   npm run generate
   ```

5. Run database migration :

   ```
   npm run migrate
   ```

6. Test with postman. Please import documentation from `/docs` into postman for testing all api available

7. Start the server:

   ```
   npm run start:dev
   ```

## License

This project is licensed under the ISC License.
