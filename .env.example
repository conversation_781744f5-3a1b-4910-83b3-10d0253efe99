#APP
APP_HOST=localhost
APP_PORT=8124
APP_PASSPORT_SECRET="secret with minimum length of 32 characters"
APP_JWT_SECRET="secret"

#JWT
SECRET_KEY_JWT= secret
PASSWORD_SALT= 10

#DB
DATABASE_URL="postgres://postgres@localhost:5432/mbh_oreosocola"
# DATABASE_URL="postgres://postgres.zocddlrvntiumiuamcxs:<EMAIL>:5432/postgres"


#SSO
GOOGLE_OAUTH2_CLIENT_ID = 
GOOGLE_OAUTH2_CLIENT_SECRET =  

FACEBOOK_APP_ID = 
FACEBOOK_APP_SECRET = 

CALLBACK_URL_BACK_END = http://localhost:8124/api/v1