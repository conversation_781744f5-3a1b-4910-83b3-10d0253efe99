import { FastifyReply } from "fastify";

import type { PaginationType, ResponseType } from "@/types";

export const customResponse = <T>(
  reply: FastifyReply,
  result: T,
  status: number,
  message?: string,
  pagination?: PaginationType
): void => {
  const resultPrint: ResponseType<T> = {};

  if (status) resultPrint.status = status;
  if (result) resultPrint.data = result;
  if (message) resultPrint.message = message;
  if (pagination) resultPrint.pagination = pagination;

  reply.status(status).send(resultPrint);
};
