export interface PaginationType {
  page: number;
  limit: number;
  totalData: number;
  totalPage: number;
}

export interface ResponseType<T> {
  status?: number;
  statusCode?: number;
  data?: T;
  message?: string;
  pagination?: PaginationType;
}

export interface ResponseHelperParamsType<T> {
  res: Response;
  result: T;
  status: number;
  message?: string;
  pagination?: PaginationType;
}

export interface QueryParamsType {
  page?: number;
  limit?: number;
  search?: string;
  searchBy?: string;
  sortBy?: string;
  sort?: string;
}

export interface GetByPaginationParamsType {
  page?: number | 1;
  limit?: number | 10;
  offset?: number | 0;
  search?: string;
  searchBy?: string | "id";
  sortBy?: string | "id";
  sort?: "asc" | "desc";
}

export interface CustomErrorType extends Error {
  statusCode?: number;
}

export interface getORMType {
  key: string;
  value: any;
  populates: string[];
}

export interface createORMType {
  data: any;
  dbInstance?: any;
}

export interface updateORMType {
  data: any;
  dbInstance?: any;
}

export interface deleteORMType {
  id: any;
  dbInstance?: any;
}

export interface getORMType {
  key: string;
  value: any;
  populates?: string[];
  dbInstance?: any;
}

export interface buildNestedQueryType {
  populates: string[];
}
