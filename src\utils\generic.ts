/* eslint-disable no-undef */
export const convertToBody = (reqBody: any) => {
  const body = Object.fromEntries(
    Object.keys(reqBody).map((key) => [key, reqBody[key].value])
  ); // Request body in key-value pairs, like req.body in Express (Node 12+)
  return body;
};

export function base64ToBufferAsync(base64: any) {
  const dataUrl = "data:application/octet-binary;base64," + base64;

  fetch(dataUrl)
    .then((res) => res.arrayBuffer())
    .then((buffer) => {
      console.log("base64 to buffer: " + new Uint8Array(buffer));
    });
}

// buffer to base64
export function bufferToBase64Async(buffer: any) {
  const blob = new Blob([buffer], { type: "application/octet-binary" });
  console.log("buffer to blob:" + blob);

  const fileReader = new FileReader();
  fileReader.onload = function () {
    const dataUrl = fileReader.result as string;
    console.log("blob to dataUrl: " + dataUrl);

    const base64 = dataUrl.substr(dataUrl.indexOf(",") + 1) as string;
    console.log("dataUrl to base64: " + base64);
  };
  fileReader.readAsDataURL(blob);
}

// Function to convert base64 to buffer
export function base64ToBuffer(base64: any) {
  // Validate base64 string
  if (!base64.startsWith("data:image/")) {
    throw new Error("Invalid image data URL.");
  }

  // Extract base64 string
  const base64Image = base64.split(";base64,").pop();

  // Convert to buffer
  return Buffer.from(base64Image, "base64");
}

export async function convertToBuffer(
  input: { value?: string | Buffer; toBuffer?: () => Promise<Buffer> } | string
): Promise<Buffer> {
  let file: Buffer;

  if (typeof input === "string" && input.startsWith("data:image/")) {
    // Handle base64 string input
    const base64Image = input.split(";base64,").pop();
    if (!base64Image) {
      throw new Error("Invalid base64 string format.");
    }
    file = Buffer.from(base64Image, "base64");
  } else if (
    typeof input === "object" &&
    input.value &&
    typeof input.value === "string" &&
    input.value.startsWith("data:image/")
  ) {
    // Handle base64 string input within an object
    const base64Image = input.value.split(";base64,").pop();
    if (!base64Image) {
      throw new Error("Invalid base64 string format.");
    }
    file = Buffer.from(base64Image, "base64");
  } else if (
    typeof input === "object" &&
    input.value &&
    Buffer.isBuffer(input.value)
  ) {
    // Handle Buffer input within an object
    file = input.value;
  } else if (
    typeof input === "object" &&
    typeof input.toBuffer === "function"
  ) {
    // Handle objects with toBuffer method
    file = await input.toBuffer();
  } else {
    throw new Error(
      "Invalid input format. Expected an image object, a Buffer, or a base64 string."
    );
  }

  return file;
}
