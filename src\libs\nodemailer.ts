import nodemailer from "nodemailer";

import { env } from "@/utils";

interface SendEmailOptions {
  email: string;
  subject?: string;
  type: string;
  data: {
    [key: string]: string;
  };
}

const OTPMailData = [
  {
    type: "otp",
    subject: "OTP Verification",
    html: `<div style="font-family: Arial, sans-serif; color: #1f1f1f;">
    <h1 style="color: #009cfc;">OTP Verification</h1>
    <h2>Hello {{email}}</h2>
    <p>Thank you for joining us. Please use the following One-Time Password (OTP) to complete your verification:</p>
    <h3 style="color: #009cfc;">{{otp}}</h3>
    <p>This OTP is valid for a limited time. Please do not share it with anyone.</p>
    <p>If you did not request this OTP, please ignore this email or contact support.</p>
    <p>Your Company</p>
  </div>`,
  },
];

const sendEmail = async ({
  email,
  type,
  data,
  subject,
}: SendEmailOptions): Promise<void | Error> => {
  try {
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: env.SMTP_EMAIL_USER,
        pass: env.SMTP_EMAIL_PASS,
      },

      // host: env.SMTP_HOST,
      // service: env.SMTP_SERVICE,
      // port: Number(env.SMTP_EMAIL_PORT),
      // secure: Boolean(env.SMTP_SECURE),
    });

    const mailData = OTPMailData.find((mail) => mail.type === type);
    if (!mailData) {
      throw new Error("Email type not found");
    }

    const html = await mailData.html
      .replace("{{email}}", data.email)
      .replace("{{otp}}", data.otp);

    await transporter.sendMail({
      from: env.SMTP_EMAIL_USER,
      to: email,
      subject: subject || mailData.subject,
      html: html,
    });

    // console.log("email sent successfully");
  } catch (error) {
    // console.log("email not sent!");
    console.error(error);
    throw new Error("Email not sent");
  }
};

export default sendEmail;
