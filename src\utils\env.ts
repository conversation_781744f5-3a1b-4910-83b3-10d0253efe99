import { config } from "dotenv";

config();

const env = {
  APP_MODE: process.env.APP_MODE || "development",
  APP_JWT_SECRET: process.env.APP_JWT_SECRET,
  APP_PASSPORT_SECRET: process.env.APP_PASSPORT_SECRET || "secret",
  HOST: process.env.HOST || "localhost",
  PORT: process.env.PORT || 3000,
  DATABASE_URL:
    process.env.DATABASE_URL ||
    "postgres://postgres:postgres@localhost:5432/postgres",
  DATABASE_SSL: process.env.DATABASE_SSL === "false" ? false : true,

  CALLBACK_URL_BACK_END:
    process.env.CALLBACK_URL_BACK_END || "http://localhost:3000",
  CALLBACK_SUCCESS_URL_FRONT_END:
    process.env.CALLBACK_SUCCESS_URL_FRONT_END || "http://localhost:3000",

  GOOGLE_OAUTH2_CLIENT_ID: process.env.GOOGLE_OAUTH2_CLIENT_ID,
  GOOGLE_OAUTH2_CLIENT_SECRET: process.env.GOOGLE_OAUTH2_CLIENT_SECRET,
  GOOGLE_VISION_API_KEY: process.env.GOOGLE_VISION_API_KEY,
  LUXAND_API_KEY: process.env.LUXAND_API_KEY,

  FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID,
  FACEBOOK_APP_SECRET: process.env.FACEBOOK_APP_SECRET,

  MBH_CMS_API_URL: process.env.MBH_CMS_API_URL,
  MBH_CMS_API_KEY: process.env.MBH_CMS_API_KEY,

  FRONT_END_URL: process.env.FRONT_END_URL,

  HUBSPOT_API_KEY: process.env.HUBSPOT_API_KEY,
  HUBSPOT_BAERER_TOKEN: process.env.HUBSPOT_BAERER_TOKEN,

  REPLICATE_API_TOKEN: process.env.REPLICATE_API_TOKEN,
  AWS_BUCKET_NAME: process.env.AWS_BUCKET_NAME,
  AWS_REGION: process.env.AWS_REGION,
  AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,

  SMTP_USER: process.env.SMTP_USER,
  SMTP_PASS: process.env.SMTP_PASS,
  SMTP_SERVICE: process.env.SMTP_SERVICE,
  SMTP_HOST: process.env.SMTP_HOST,
  SMTP_PORT: process.env.SMTP_PORT,
  SMTP_SECURE: process.env.SMTP_SECURE,
  SMTP_EMAIL_USER: process.env.SMTP_EMAIL_USER,
  SMTP_EMAIL_PASS: process.env.SMTP_EMAIL_PASS,
  SMTP_EMAIL_PORT: process.env.SMTP_EMAIL_PORT,
  SMTP_EMAIL_SECURE: process.env.SMTP_EMAIL_SECURE,
  SMTP_EMAIL_HOST: process.env.SMTP_EMAIL_HOST,
  SMTP_EMAIL_SERVICE: process.env.SMTP_EMAIL_SERVICE,

  DB: {
    HOST: process.env.DB_HOST,
    USER: process.env.DB_USER,
    PASSWORD: process.env.DB_PASSWORD,
    DATABASE: process.env.DB_DATABASE,
    PORT: process.env.DB_PORT || 5432,
    SSL: process.env.DB_SSL === "false" ? false : true,
    URL: process.env.DB_URL,

    HOST_EXTERNAL: process.env.DB_HOST_EXTERNAL,
    USER_EXTERNAL: process.env.DB_USER_EXTERNAL,
    PASSWORD_EXTERNAL: process.env.DB_PASSWORD_EXTERNAL,
    DATABASE_EXTERNAL: process.env.DB_DATABASE_EXTERNAL,
    PORT_EXTERNAL: process.env.DB_PORT_EXTERNAL || 5432,
    SSL_EXTERNAL: process.env.DB_SSL_EXTERNAL === "false" ? false : true,
    URL_EXTERNAL: process.env.DB_URL_EXTERNAL,
  },

  MAIL_API_KEY: process.env.MAIL_API_KEY || "xk-b",
  MAIL_API_URL:
    process.env.MAIL_API_URL || " ",
  MAIL_SENDER: process.env.MAIL_SENDER || " ",
  MAIL_RECEIVER: process.env.MAIL_RECEIVER || "",

  API_LIMIT_API_CALL: process.env.API_LIMIT_API_CALL || 54000,
};

// Log the extracted environment variables
console.log(env);

export default env;
