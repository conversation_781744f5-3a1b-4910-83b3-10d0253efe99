import { FastifyInstance } from "fastify";

// import { authValidation } from "@/middlewares/auth.middleware";
import controllers from "../controllers/otp-verifications.controller";
// import { $ref } from "../schemas";
// import { fastifyPassport } from "../";

async function otpVerificationsRouter(fastify: FastifyInstance) {
  fastify.route({
    method: "POST",
    url: "/verify",
    // schema: {
    //   body: $ref("otpVerificationsRegisterSchema"),
    // },
    handler: controllers.otpVerification,
  });
  fastify.route({
    method: "POST",
    url: "/generate",
    // schema: {
    //   body: $ref("otpVerificationsRegisterSchema"),
    // },
    handler: controllers.otpGenerate,
  });
}

export default otpVerificationsRouter;
