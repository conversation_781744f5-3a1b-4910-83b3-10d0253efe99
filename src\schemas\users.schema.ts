import { z } from "zod";

export const usersSchemas = z.object({
  zaloNumber: z.string().min(6).max(255),
});

export type UsersSchemasType = z.infer<typeof usersSchemas>;

// export const usersLoginSchemas = z.object({
//   body: usersSchemas,
// });

// export const usersRegisterSchemas = z.object({
//   body: usersSchemas,
// });

 
export type UsersRegisterSchemasType = z.infer<typeof usersRegisterSchema>;
export const usersRegisterSchemas = z.object({
  body: usersRegisterSchema,
});

export const usersLoginSchema = z.object({
  email: z.string().email(),
  pin: z.string().min(6).max(255),
});
export type UsersLoginSchemaType = z.infer<typeof usersLoginSchema>;
export const usersLoginSchemas = z.object({
  body: usersLoginSchema,
});
