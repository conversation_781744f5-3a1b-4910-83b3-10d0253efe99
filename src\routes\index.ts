import { FastifyInstance } from "fastify";

import { schemas } from "@/schemas";

import externalApisRouter from "./external-apis.route";
import formsRoutes from "./forms.route";
import otpVerificationsRoutes from "./otp-verifications.route";
// import {
//   serializerCompiler,
//   validatorCompiler,
//   ZodTypeProvider,
// } from "fastify-type-provider-zod";
import usersRoutes from "./users.route";

export default function routes(app: FastifyInstance) {
  const prefixUrl = "/api/v1";

  // inject custom type zod to fastify
  // app.withTypeProvider<ZodTypeProvider>();
  // app.setValidatorCompiler(validatorCompiler);
  // app.setSerializerCompiler(serializerCompiler);

  // custom manual schema only with schema
  for (const schema of [...schemas]) {
    app.addSchema(schema);
  }

  app.register(usersRoutes, { prefix: `${prefixUrl}/users` });
  app.register(otpVerificationsRoutes, { prefix: `${prefixUrl}/otp` });
  app.register(formsRoutes, { prefix: `${prefixUrl}/forms` });
  app.register(externalApisRouter, { prefix: `${prefixUrl}/external` });

  // Swagger UI
  // app.register(fastifySwaggerUI, {
  //   routePrefix: '/documentation',
  // });
}
