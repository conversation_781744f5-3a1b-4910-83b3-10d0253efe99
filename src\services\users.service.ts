import bcrypt from "bcrypt";
import { eq } from "drizzle-orm";
import { FastifyReply, FastifyRequest } from "fastify";

import db from "@/db";
import { users } from "@/db/schemas";
import { CustomErrorType } from "@/types";
import { env } from "@/utils";

const register = async (data: any) => {
  try {
    const {
    
      lastPlayGame = "test",
      dateOfBirth = "28-03-2000",
      collectedPoints = "200",

      tnc = "true",

      overallOptInStatus = "false",

      cid = "GA1.2.106822649.1670999798",

      utmCampaign = "space_dunk",
      utmContent = "testing",
      utmMedium = "website",
      utmSource = "google",
      utmTerm = "test",

      hubspotutk,
    } = data;

    //* EXECUTE HUBSPOT FORM
    const barierTokenHubspot = "Bearer " + env.HUBSPOT_BAERER_TOKEN;

    const urlHubspotForm = `https://api.hubapi.com/contacts/v1/contact/createOrUpdate/email/${email}/`;
    const formDataHubspot = {
      properties: [
        
        {
          property: "last_play_game",
          value: lastPlayGame,
        },
        
        {
          property: "collected_points",
          value: collectedPoints,
        },
        {
          property: "overall_opt_in_status",
          value: overallOptInStatus,
        },
        {
          property: "tnc",
          value: tnc,
        },
        {
          property: "cid",
          value: cid,
        },
        {
          property: "utm_campaign",
          value: utmCampaign,
        },
        {
          property: "utm_content",
          value: utmContent,
        },
        {
          property: "utm_medium",
          value: utmMedium,
        },
        {
          property: "utm_source",
          value: utmSource,
        },
        {
          property: "utm_term",
          value: utmTerm,
        },
        {
          property: "source",
          value: "Multi market | Space Dunk | Oreo | 2025",
        },
      ],
    };
    const res = await fetch(urlHubspotForm, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: barierTokenHubspot,
      },
      body: JSON.stringify(formDataHubspot),
    })
      .then((res) => res.json())
      .then((json) => {
        return json;
      })
      .catch((error) => console.error("Error:", error));
    if (res.status === "error") {
      console.log("ERROR HUBSPOT SUBMIT PARTICIPANT" + JSON.stringify(res));
      const error: CustomErrorType = new Error(
        "Failed to submit register form"
      );
      error.statusCode = 404;
      throw error;
    }

    const username = `${firstName.toLowerCase()}${lastName.toLowerCase()}${phone.slice()}`;

    // insert into db
    const createdUser = await db().insert(users).values({
      point: 0,
      latest_usage_bonus_level: null,
    });

    if (!createdUser) {
      const error: CustomErrorType = new Error("Failed to create user");
      error.statusCode = 404;
      throw error;
    }

    console.log("RESULT BODY REGISTER " + JSON.stringify(data));
    console.log("RESULT HUBSPOT REGISTER " + JSON.stringify(res));

    return createdUser;
  } catch (err) {
    console.error(`Unexpected error: ${err}`);
    throw err;
  }
};

const login = async (email: string, pin: string, reply: FastifyReply) => {
  try {
    // check user
    const findUser = await db()
      .select()
      .from(users)
      .where(eq(users.email, email.toLowerCase()));

    if (!findUser || findUser.length === 0) {
      const error: CustomErrorType = new Error("User not found");
      error.statusCode = 404;
      throw error;
    }

    const hashedPassword = findUser[0].password || "";
    const isPasswordMatch = await bcrypt.compare(pin, hashedPassword);

    if (!isPasswordMatch) {
      const error: CustomErrorType = new Error("Password is incorrect");
      error.statusCode = 400;
      throw error;
    }
    const userId = findUser[0].id;
    const firstName = findUser[0].first_name;
    const lastName = findUser[0].last_name;
    const username = findUser[0].username;

    const token = await reply.jwtSign(
      {
        userId: userId,
      },
      { expiresIn: "12h" }
    );
    const refreshToken = await reply.jwtSign(
      { userId: userId },
      { expiresIn: "30d" }
    );

    return {
      token,
      refreshToken,
      userData: findUser[0],
      duration: 43200, // 12 hours,
    };
  } catch (err) {
    console.error(`Unexpected error: ${err}`);
    throw err;
  }
};

// TODO RESET LEVEL HISTORY EVERYDAY
// TODO RESET BONUS POINT IF LATEST 7 DAYS AGO
const resetUserScheduler = async (data: any) => {
  try {
    // TODO RESET LEVEL HISTORY EVERYDAY
    // TODO RESET BONUS POINT IF LATEST 7 DAYS AGO
  } catch (err) {
    console.error(`Unexpected error: ${err}`);
    throw err;
  }
};

// TODO MAKED HISTORY LEVEL OR USER EXPORT
const exportUser = async (data: any) => {
  try {
    // TODO MAKED HISTORY LEVEL OR USER EXPORT
  } catch (err) {
    console.error(`Unexpected error: ${err}`);
    throw err;
  }
};

// const googleAuth = async (req: FastifyRequest) => {
//   console.log(req);
//   return;
// };

// const googleAuthCb = async (json: any, reply: FastifyReply) => {
//   try {
//     if (!json) {
//       throw new Error("Failed to login");
//     }

//     const googleId = json.id;
//     const email = json._json.email;
//     const name = json._json.name;

//     const data = {
//       email: email,
//       name: name,
//       gId: googleId,
//       status: "active",
//     };

//     const url = `${env.MBH_CMS_API_URL}/api/v1/public/hooks/participant/find?email=${email}&compress=true&extractCompress=true`;
//     console.log("URL CMS URL " + url);
//     const api_key = env.MBH_CMS_API_KEY || "";
//     console.log("URL CMS API KEY " + api_key);

//     let isNewParticipant = false;
//     let result = null;

//     try {
//       const res = await fetch(url, {
//         method: "GET",
//         headers: {
//           "Content-Type": "application/json",
//           api_key,
//         },
//         // body: JSON.stringify(data),
//       });

//       if (!res.ok) {
//         isNewParticipant = true;
//         console.error("ERROR CMS SSO GOOGLE:", res.statusText);
//       } else {
//         result = await res.json();

//         // update participant
//       }
//     } catch (error) {
//       console.error("ERROR CMS SSO GOOGLE:", error);
//     }

//     let dataFormUpdate = {
//       ...result?.data,
//       status: "active",
//       googleId: googleId,
//     };

//     if (!dataFormUpdate?.dynamicPrompt) {
//       const finalRandomizerFeel = await randomizerPrompt(
//         "no-update-randomizer",
//         result || null
//       );

//       dataFormUpdate = {
//         ...dataFormUpdate,
//         dynamicPrompt: JSON.stringify(finalRandomizerFeel),
//       };
//     }

//     if (!dataFormUpdate?.firstName) {
//       dataFormUpdate = {
//         ...dataFormUpdate,
//         firstName: name?.split(" ")?.[0],
//       };
//     }

//     if (dataFormUpdate?.googleId !== googleId && !isNewParticipant) {
//       //* PROMPT RANDOMIZER
//       const urlUpdate = `${env.MBH_CMS_API_URL}/api/v1/public/hooks/participant`;
//       const results = await fetch(urlUpdate, {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//           api_key: api_key,
//         },
//         body: JSON.stringify(dataFormUpdate),
//       })
//         .then((res) => res.json())
//         .then((json) => {
//           return json;
//         });

//       console.log("UPDATE SSO CMS GOOGLE " + results);
//     }

//     const token = await reply.jwtSign(
//       {
//         userId: await dataFormUpdate?.id,
//         firstName: await dataFormUpdate?.firstName,
//         lastName: await dataFormUpdate?.lastName,
//         email: await dataFormUpdate?.email,
//       },
//       { expiresIn: "12h" }
//     );
//     const refreshToken = await reply.jwtSign(
//       { userId: await dataFormUpdate?.id },
//       { expiresIn: "30d" }
//     );

//     console.log("RESULT CMS SSO GOOGLE" + JSON.stringify(result));

//     let userData: any = {
//       email: email,
//       name: name,
//       gId: googleId,
//       isNewParticipant: isNewParticipant,
//     };
//     if (dataFormUpdate) {
//       userData = {
//         ...dataFormUpdate,
//         ...userData,
//       };
//       delete userData?.dynamicPrompt;
//     }

//     console.log("DATA CMS SSO GOOGLE " + JSON.stringify(userData));
//     return {
//       token,
//       refreshToken,
//       userData: userData,
//       duration: 43200, // 12 hours,
//     };
//   } catch (err) {
//     console.error(`Unexpected error: ${err}`);
//     throw err;
//   }
// };

// const facebookAuth = async (req: FastifyRequest) => {
//   console.log(req);
//   return;
// };

// const facebookAuthCb = async (json: any, reply: FastifyReply) => {
//   try {
//     if (!json) {
//       throw new Error("Failed to login");
//     }

//     const facebookId = json.id;
//     const email = json._json.email;
//     const name = json._json.name;

//     const data = {
//       email: email,
//       name: name,
//       fbId: facebookId,
//       status: "active",
//     };
//     const url = `${env.MBH_CMS_API_URL}/api/v1/public/hooks/participant/find?email=${email}&compress=true&extractCompress=true`;
//     console.log("URL CMS URL " + url);
//     const api_key = env.MBH_CMS_API_KEY || "";
//     console.log("URL CMS API KEY " + api_key);

//     let isNewParticipant = false;
//     let result = null;

//     try {
//       const res = await fetch(url, {
//         method: "GET",
//         headers: {
//           "Content-Type": "application/json",
//           api_key,
//         },
//         // body: JSON.stringify(data),
//       });

//       if (!res.ok) {
//         isNewParticipant = true;
//         console.error("ERROR CMS SSO FB:", res.statusText);
//       } else {
//         result = await res.json();
//       }
//     } catch (error) {
//       console.error("ERROR CMS SSO FB:", error);
//     }

//     let dataFormUpdate = {
//       ...result?.data,
//       status: "active",
//       facebookId: facebookId,
//     };

//     if (!dataFormUpdate?.dynamicPrompt) {
//       const finalRandomizerFeel = await randomizerPrompt(
//         "no-update-randomizer",
//         result || null
//       );

//       dataFormUpdate = {
//         ...dataFormUpdate,
//         dynamicPrompt: JSON.stringify(finalRandomizerFeel),
//       };
//     }

//     if (!dataFormUpdate?.firstName) {
//       dataFormUpdate = {
//         ...dataFormUpdate,
//         firstName: name?.split(" ")?.[0],
//       };
//     }

//     if (dataFormUpdate?.facebookId !== facebookId && !isNewParticipant) {
//       //* PROMPT RANDOMIZER
//       const urlUpdate = `${env.MBH_CMS_API_URL}/api/v1/public/hooks/participant`;
//       const results = await fetch(urlUpdate, {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//           api_key: api_key,
//         },
//         body: JSON.stringify(dataFormUpdate),
//       })
//         .then((res) => res.json())
//         .then((json) => {
//           return json;
//         });

//       console.log("UPDATE SSO CMS FB " + results);
//     }

//     const token = await reply.jwtSign(
//       {
//         userId: await dataFormUpdate?.id,
//         firstName: await dataFormUpdate?.firstName,
//         lastName: await dataFormUpdate?.lastName,
//         email: await dataFormUpdate?.email,
//       },
//       { expiresIn: "12h" }
//     );

//     const refreshToken = await reply.jwtSign(
//       { userId: await dataFormUpdate?.id },
//       { expiresIn: "30d" }
//     );

//     console.log("RESULT CMS SSO FB" + JSON.stringify(result));

//     let userData: any = {
//       email: email,
//       name: name,
//       fbId: facebookId,
//       isNewParticipant: isNewParticipant,
//     };
//     if (result?.data) {
//       userData = {
//         ...result?.data,
//         ...userData,
//       };
//       delete userData?.dynamicPrompt;
//     }

//     console.log("DATA CMS SSO FB " + JSON.stringify(userData));
//     return {
//       token,
//       refreshToken,
//       userData: userData,
//       duration: 43200, // 12 hours,
//     };
//   } catch (err) {
//     console.error(`Unexpected error: ${err}`);
//     throw err;
//   }
// };

// const zaloAuth = async (req: FastifyRequest) => {
//   console.log(req);
//   return;
// };

// const zaloAuthCb = async (req: FastifyRequest) => {
//   console.log(req);
//   return;
// };

// const tiktokAuth = async (req: FastifyRequest) => {
//   console.log(req);
//   return;
// };

// const tiktokAuthCb = async (req: FastifyRequest) => {
//   console.log(req);
//   return;
// };

export default {
  register,
  login,
  // googleAuth,
  // googleAuthCb,
  // facebookAuth,
  // facebookAuthCb,
  // zaloAuth,
  // tiktokAuth,
  // tiktokAuthCb,
  // zaloAuthCb,
};
