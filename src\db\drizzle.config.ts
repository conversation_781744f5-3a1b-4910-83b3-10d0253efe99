import { defineConfig } from "drizzle-kit";

import env from "@/utils/env";

export default defineConfig({
  schema: "./src/db/schemas/*.ts",
  out: "./src/db/migrations/",
  dialect: "postgresql",
  dbCredentials: {
    host: env.DB.HOST,
    user: env.DB.USER,
    password: env.DB.PASSWORD,
    database: env.DB.DATABASE,
    port: Number(env.DB.PORT),
    ssl: env.DB.SSL,
    url:
      env.DATABASE_URL ||
      "postgres://postgres:postgres@localhost:5432/postgres",
  },
  // breakpoints: false,
  // verbose: true,
  // strict: true,
});
