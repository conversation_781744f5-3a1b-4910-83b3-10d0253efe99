import { FastifyReply } from "fastify";

import { CustomErrorType } from "@/types";

import { customResponse } from ".";

export const customError = (error: unknown, reply: FastifyReply) => {
  if (error instanceof Error) {
    const customError = error as CustomErrorType;
    return customResponse(
      reply,
      null,
      customError.statusCode || 500,
      customError.message || "Something When Wrong"
    );
  } else {
    return customResponse(
      reply,
      null,
      500,
      (error as string) || "Something When Wrong"
    );
  }
};

export default customError;
