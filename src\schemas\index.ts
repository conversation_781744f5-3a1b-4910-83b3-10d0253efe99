import { buildJsonSchemas } from "fastify-zod";
import { z } from "zod";

// import { paymentAccountsSchemas } from "./paymentAccounts.schema";
import {
  usersLoginSchema,
  usersLoginSchemas,
  usersRegisterSchema,
  usersRegisterSchemas,
  usersSchemas,
} from "./users.schema";

export const idSchemas = z.object({
  id: z.string(),
});

export type ParamSchemasType = z.infer<typeof idSchemas>;

export const { schemas: schemas, $ref } = buildJsonSchemas({
  // common schemas
  idSchemas: idSchemas,

  // payment accounts schemas
  // paymentAccountsSchemas: paymentAccountsSchemas,

  // users schemas
  usersSchemas: usersSchemas,
  usersLoginSchemas: usersLoginSchemas,
  usersRegisterSchemas: usersRegisterSchemas,
  usersLoginSchema,
  usersRegisterSchema,
});
