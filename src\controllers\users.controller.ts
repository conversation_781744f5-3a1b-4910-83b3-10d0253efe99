import { FastifyReply, FastifyRequest } from "fastify";

import { customError, customResponse } from "@/helpers";
import { UsersLoginSchemaType } from "@/schemas/users.schema";
import otpVerificationServices from "@/services/otp-verifications.service";
import services from "@/services/users.service";

const controllers = {
  register: async (req: any, reply: FastifyReply) => {
    try {
      await services.register(req.body);

      if (req.body.email) {
        await otpVerificationServices.otpGenerate({
          email: req.body.email,
        });
      }

      return customResponse(reply, null, 200, "Register Success");
    } catch (error) {
      return customError(error, reply);
    }
  },
  login: async (
    req: FastifyRequest<{
      Body: UsersLoginSchemaType;
    }>,
    reply: FastifyReply
  ) => {
    try {
      const { email, pin } = req.body;

      // await otpVerificationServices.otpGenerate({
      //   email: email,
      // });
      await services.login(email, pin, reply);

      return customResponse(reply, null, 200, "Login Success");
    } catch (error) {
      return customError(error, reply);
    }
  },
};
export default controllers;
