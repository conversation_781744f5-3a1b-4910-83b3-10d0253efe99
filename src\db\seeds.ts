import { eq } from "drizzle-orm";

import { counts } from "./schemas/index";
import db from "./index";

export const seedCountTable = async () => {
  const dbInstance = db();
  try {
    const existingSuccess = await dbInstance
      .select()
      .from(counts)
      .where(eq(counts.status, "success"))
      .execute();

    const existingFailed = await dbInstance
      .select()
      .from(counts)
      .where(eq(counts.status, "failed"))
      .execute();

    if (existingSuccess.length === 0) {
      await dbInstance.insert(counts).values({ status: "success", total: 0 });
      console.log("Inserted 'success' status.");
    } else {
      console.log("'success' status already exists. Skipping insertion.");
    }

    if (existingFailed.length === 0) {
      await dbInstance.insert(counts).values({ status: "failed", total: 0 });
      console.log("Inserted 'failed' status.");
    } else {
      console.log("'failed' status already exists. Skipping insertion.");
    }

    console.log("Count table seeding process completed.");
    process.exit(0); // Exit with success code
  } catch (error) {
    console.error("Error during count table seeding process:", error);
    process.exit(1); // Exit with error code
  }
};

seedCountTable();
