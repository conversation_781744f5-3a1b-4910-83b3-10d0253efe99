import bcrypt from "bcrypt";
import { eq } from "drizzle-orm";
import { FastifyReply } from "fastify";
import { env } from "process";

import db from "@/db";
import { otp, users } from "@/db/schemas";
import { CustomErrorType } from "@/types";

const services = {
  generateUniqueOtp: async () => {
    try {
      let otpCode;
      let isUnique = false;

      while (!isUnique) {
        // Generate OTP code
        otpCode = Math.floor(100000 + Math.random() * 900000).toString();

        // Check if the OTP exists in the database
        const findOtp = await db()
          .select()
          .from(otp)
          .where(eq(otp.code, otpCode));

        if (!findOtp || findOtp.length === 0) {
          isUnique = true; // OTP is unique, exit the loop
        }
      }

      return otpCode;
    } catch (err) {
      console.error(`Unexpected error: ${err}`);
      throw err;
    }
  },

  otpGenerate: async (data: { email?: string }) => {
    try {
      // generate otp code 6 code
      const otpCode = await services.generateUniqueOtp();

      const findUser = await db()
        .select()
        .from(users)
        .where(eq(users.email, data.email || ""));
      if (!findUser) {
        const error: CustomErrorType = new Error("User not found");
        error.statusCode = 404;
        throw error;
      }

      // save otp code to database
      const otpCreated = await db()
        .insert(otp)
        .values({
          code: otpCode,
          user_id: findUser[0].id,
          expired_at: new Date(Date.now() + 1000 * 60 * 5), // 5 minutes
        });
      if (!otpCreated) {
        const error: CustomErrorType = new Error("Failed to Generate OTP");
        error.statusCode = 400;
        throw error;
      }

      // send otp code to email
      const urlHubspot =
        "https://api.hubapi.com/marketing/v4/email/single-send";
      const bodyHubspot = {
        emailId: 174486932398,
        message: {
          to: data.email || "", // To email id
        },
        contactProperties: [
          {
            name: "otp___oreo",
            value: otpCode,
          },
        ],
      };
      const barierTokenHubspot = "Bearer " + env.HUBSPOT_BAERER_TOKEN;

      const resultHubspot = await fetch(urlHubspot, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: barierTokenHubspot,
        },
        body: JSON.stringify(bodyHubspot),
      })
        .then((res) => {
          return res.json();
        })
        .catch((error) => console.error("Error:", error));

      if (resultHubspot?.status === "error") {
        const error: CustomErrorType = new Error("Failed to send Mail OTP");
        error.statusCode = 404;
        throw error;
      }

      console.log(
        "RESULT HUBSPOT GENERATE OTP" + JSON.stringify(resultHubspot)
      );
      console.log("RESULT CMS GENERATE OTP" + JSON.stringify(otpCreated));

      return resultHubspot;
    } catch (err) {
      console.error(`Unexpected error: ${err}`);
      throw err;
    }
  },

  otpVerification: async ({
    code,
    type,
    email,
    pin,
    reply,
  }: {
    code: number;
    type: "register" | "login" | "forgotPassword"; // "register" | "login
    email: string;
    pin?: string;
    reply: FastifyReply;
  }) => {
    try {
      // find code otp in database
      const findOtp = await db()
        .select()
        .from(otp)
        .where(eq(otp.code, code.toString()));

      if (!findOtp) {
        const error: CustomErrorType = new Error("OTP not found");
        error.statusCode = 404;
        throw error;
      }

      // check expired otp
      if (findOtp[0].expired_at < new Date()) {
        const error: CustomErrorType = new Error("OTP expired");
        error.statusCode = 400;
        throw error;
      }

      // delete otp code in database
      await db().delete(otp).where(eq(otp.code, code.toString()));

      // get user id from database
      const findUser = await db()
        .select()
        .from(users)
        .where(eq(users.email, email));

      if (!findUser) {
        const error: CustomErrorType = new Error("User not found");
        error.statusCode = 404;
        throw error;
      }
      if (
        (type === "register" || type === "forgotPassword") &&
        pin &&
        pin.length > 0
      ) {
        const password = pin;
        const hashedPassword = await bcrypt.hash(password, 10);

        await db()
          .update(users)
          .set({
            password: hashedPassword,
          })
          .where(eq(users.id, findUser[0].id));
      }

      if (type === "forgotPassword") {
        return null;
      }

      const userId = findUser[0].id;
      const firstName = findUser[0].first_name;
      const lastName = findUser[0].last_name;
      const username = findUser[0].username;

      const token = await reply.jwtSign(
        {
          userId: userId,
          firstName: firstName,
          lastName: lastName,
          email: email,
          username: username,
        },
        { expiresIn: "12h" }
      );
      const refreshToken = await reply.jwtSign(
        { userId: userId },
        { expiresIn: "30d" }
      );

      const finalResult = {
        token,
        refreshToken,
        user: findUser[0],
        duration: 43200, // 12 hours,
      };

      console.log("RESULT CMS VERIFY OTP" + JSON.stringify(finalResult));

      return finalResult;
    } catch (err) {
      console.error(`Unexpected error: ${err}`);
      throw err;
    }
  },
};

export default services;
