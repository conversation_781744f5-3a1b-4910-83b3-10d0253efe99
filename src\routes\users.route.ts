import { FastifyInstance } from "fastify";

// import { authValidation } from "@/middlewares/auth.middleware";
import controllers from "../controllers/users.controller";
import { $ref } from "../schemas";
// import { fastifyPassport } from "../";

async function usersRouter(fastify: FastifyInstance) {
  fastify.route({
    method: "POST",
    url: "/login",
    schema: {
      body: $ref("usersLoginSchema"),
    },
    handler: controllers.login,
  });
  fastify.route({
    method: "POST",
    url: "/register",
    // schema: {
    //   body: $ref("usersRegisterSchema"),
    // },
    handler: controllers.register,
  });
}

export default usersRouter;
