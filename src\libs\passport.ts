import { Authenticator } from "@fastify/passport";
import { Strategy as FacebookStrategy } from "passport-facebook";
import { Strategy as GoogleStrategy } from "passport-google-oauth20";

import { env } from "@/utils";

const passportStrategy = (passport: Authenticator) => {
  passport.use(
    // "google",
    new GoogleStrategy(
      {
        clientID: env.GOOGLE_OAUTH2_CLIENT_ID || "",
        clientSecret: env.GOOGLE_OAUTH2_CLIENT_SECRET || "",
        callbackURL: `${env.CALLBACK_URL_BACK_END}/users/auth/google/callback`,
        passReqToCallback: true,
        // store: false,
      },
      async (request, accessToken, refreshToken, profile, done) => {
        try {
          return done(null, JSON.stringify(profile));
        } catch (error) {
          return done(error, false);
        }
      }
    )
  );

  passport.use(
    // "facebook",
    new FacebookStrategy(
      {
        clientID: env.FACEBOOK_APP_ID || "",
        clientSecret: env.FACEBOOK_APP_SECRET || "",
        callbackURL: `${env.CALLBACK_URL_BACK_END}/users/auth/facebook/callback`,
        profileFields: ["id", "displayName", "photos", "emails"],
        // state: true,
        // enableProof: true
      },
      function (accessToken, refreshToken, profile, cb) {
        try {
          return cb(null, JSON.stringify(profile));
        } catch (error) {
          return cb(error, false);
        }
      }
    )
  );
};

export default passportStrategy;
