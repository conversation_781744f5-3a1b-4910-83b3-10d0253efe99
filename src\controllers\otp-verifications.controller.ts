import { FastifyReply, FastifyRequest } from "fastify";

import { customError, customResponse } from "@/helpers";
import services from "@/services/otp-verifications.service";

const controllers = {
  otpGenerate: async (
    req: FastifyRequest<{
      Body: { email: string };
    }>,
    reply: FastifyReply
  ) => {
    try {
      const { email } = req.body;

      await services.otpGenerate({ email: email });

      return customResponse(reply, null, 201, "Generate OTP Success");
    } catch (error) {
      return customError(error, reply);
    }
  },
  otpVerification: async (
    req: FastifyRequest<{
      Body: {
        code: number;
        type: "register" | "login" | "forgotPassword";
        email: string;
        pin?: string;
      };
    }>,
    reply: FastifyReply
  ) => {
    try {
      const { code, type, email, pin } = req.body;

      const result = await services.otpVerification({
        code: code,
        type: type,
        email: email,
        pin: pin,
        reply: reply,
      });

      return customResponse(reply, result, 200, "OTP Verification Success");
    } catch (error) {
      return customError(error, reply);
    }
  },
};
export default controllers;
