import fastifyCookie from "@fastify/cookie";
import cors from "@fastify/cors";
import helmet from "@fastify/helmet";
import multipart from "@fastify/multipart";
import {
  // passport,
  Authenticator,
} from "@fastify/passport";
// import fastifySecureSession from "@fastify/secure-session";
import fastifySession from "@fastify/session";
import Fastify from "fastify";

import passportStrategy from "@/libs/passport";
import { authMiddlewares } from "@/middlewares/auth.middleware";
import registerRoutes from "@/routes";
import { env } from "@/utils";

const app = Fastify({
  logger: {
    transport: {
      target: "@fastify/one-line-logger",
      options: {
        colorize: true,
      },
    },
  },
  bodyLimit: 1048576 * 20, // 10MB
});

app.register(cors, {
  // options cors
  origin: "*",
});

app.register(
  helmet,
  // Example disables the `contentSecurityPolicy` middleware but keeps the rest.
  { contentSecurityPolicy: false }
);

// Register multipart
app.register(multipart, {
  // if handle keyvalue, you can use this option
  attachFieldsToBody: true,
  // attachFieldsToBody: "keyValues",
  // onFile
  throwFileSizeLimit: true,
  limits: {
    files: 1,
    fileSize: 1048576 * 10, // 10MB,
  },
});

// Register plugins
authMiddlewares(app);

// Register routes
registerRoutes(app);

// Handle all content types that matches RegExp
// fastify.addContentTypeParser(/^image\/.*/, function (request, payload, done) {
//   imageParser(payload, function (err, body) {
//     done(err, body)
//   })
// })

// Not found handler
app.setNotFoundHandler((req, res) => {
  res.status(500).send({
    message: "Internal server error",
    status: 500,
  });
});

// configurate @fastify/passport with cookie and session
const fastifyPassport = new Authenticator();
app.register(fastifyCookie);
app.register(fastifySession, {
  secret: env.APP_PASSPORT_SECRET,
});
// app.register(fastifySecureSession, {
//   key: fs.readFileSync(path.join(__dirname, "secret-key")),
// });

app.register(fastifyPassport.initialize());
app.register(fastifyPassport.secureSession());
passportStrategy(fastifyPassport);
// if session is enabled, you need to register the serializer and deserializer functions
// fastifyPassport.registerUserSerializer(async (user, request) => {
//   // return user.id;
//   console.log("user", user);
//   return user;
// });
// // ... and then a deserializer that will fetch that user from the database when a request with an id in the session arrives
// fastifyPassport.registerUserDeserializer(async (id, request) => {
//   // return await User.findById(id);
//   console.log("id", id);
// });

// Run the server
app.listen(
  {
    port: Number(env.PORT) || 3000,
    host: env.HOST || "",
  },
  (err, address) => {
    if (err) {
      app.log.error(err);
      process.exit(1);
    }
    app.log.info(`Server running on ${address}`);
  }
);

export { fastifyPassport };
