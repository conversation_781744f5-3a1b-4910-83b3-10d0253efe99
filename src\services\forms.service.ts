import { CustomErrorType } from "@/types";
import { env } from "@/utils";

export const submitForm = async (data: any) => {
  try {
    const {
      template_type,
      image_url,
      generating_image,

      firstName,
      lastName,
      email,
      phone,

      map_longitude,
      map_latitude,

      tnc = "true",

      overallOptInStatus,

      typeOfSignup,

      cid,

      utmCampaign,
      utmContent,
      utmMedium,
      utmSource,
      utmTerm,

      hubspotutk,
    } = data;

    // API KEY CMS
    const api_key = env.MBH_CMS_API_KEY || "";

    let participantExist = false;
    let contactPhoneParticipant = null;
    //* FIND PARTICIPANT BY EMAIL
    const urlParticipant = `${env.MBH_CMS_API_URL}/api/v1/public/hooks/participant/find?email=${email}&compress=true&extractCompress=true`;
    const getParticipant = await fetch(urlParticipant, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        api_key: api_key,
      },
    })
      .then((res) => res.json())
      .then((json) => {
        return json;
      })
      .catch((error) => console.error("Error:", error));
    if (!getParticipant?.statusCode?.toString()?.startsWith("2")) {
      console.log(
        "ERROR CMS SEARCHING PARTICIPANT" + JSON.stringify(getParticipant)
      );
      const error: CustomErrorType = new Error(
        getParticipant?.message || "Failed to find participant"
      );
      error.statusCode = 404;
      throw error;
    }
    if (getParticipant?.data) {
      participantExist = true;
      contactPhoneParticipant = getParticipant?.data?.phone;
    }

    //* HUBSPOT SEARCH CONTACT BY PHONE
    const barierTokenHubspot = "Bearer " + env.HUBSPOT_BAERER_TOKEN;

    let contactExist = false;
    let contactId = null;

    const urlHubspotSearchContact = `https://api.hubapi.com/crm/v3/objects/contacts/search`;
    const bodyHubspotSearchContact = {
      filterGroups: [
        {
          filters: [
            {
              propertyName: "phone",
              operator: "EQ",
              value: contactPhoneParticipant?.toString(),
            },
          ],
        },
      ],
    };
    const resSearchContact = await fetch(urlHubspotSearchContact, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: barierTokenHubspot,
      },
      body: JSON.stringify(bodyHubspotSearchContact),
    })
      .then((res) => res.json())
      .then((json) => {
        return json;
      })
      .catch((error) => console.error("Error:", error));
    if (resSearchContact?.total !== 0) {
      contactExist = true;
      contactId = resSearchContact.results[0].id;
    }

    //* SUBMIT CMS API && HUBSPOT API
    const dataForm = {
      ...getParticipant.data,
      ...data,
      firstName:
        firstName?.trim()?.length > 0
          ? firstName
          : getParticipant.data.firstName,
      lastName:
        lastName?.trim()?.length > 0 ? lastName : getParticipant.data.lastName,
      phone: phone?.trim()?.length > 0 ? phone : getParticipant.data.phone,
      tnc: tnc || "true",
      // overall_opt_in_status: overallOptInStatus,
    };
    if (overallOptInStatus) {
      dataForm["overall_opt_in_status"] = overallOptInStatus;
    }

    // ==========

    let resultHubspotForm = null;
    const bodyFormDataContact = {
      properties: {
        firstname: dataForm.firstName || "Randy",
        lastname: dataForm.lastName || "Randy",
        phone: dataForm.phone || "08123456789",

        // map_latitude: map_latitude || "765654345", //Pass the latitude value here
        // map_longitude: map_longitude || "6767236432", //Pass the longitude value here

        tnc: tnc || "true", //Terms and conditions ; Static pass this value
        overall_opt_in_status: dataForm.overallOptInStatus || "false", //Accepted values are "true" or "false"

        type_of_signup: typeOfSignup, //pass the auth type value here
        cid: cid || "GA1.2.106822649.1670999798", //Pass the google analytics client Id (_ga cookie)
        utm_campaign: utmCampaign || "test campaign", //Pass the UTM campaign value here
        utm_content: utmContent || "test", //Pass the UTM content value here
        utm_medium: utmMedium || "test", //Pass the UTM medium value here
        utm_source: utmSource || "test", //Pass the UTM source value here
        utm_term: utmTerm || "test", //Pass the UTM term value here
        source: "VN | Socola Pie- Your feeling our ad | Oreo | 2024", //Static pass this value
      },
    };
    if (!contactExist && !contactId) {
      //* SUBMIT CONTACT IS NOT FOUND TO CREATE NEW CONTACT
      const urlHubspotFormContact =
        "https://api.hubapi.com/crm/v3/objects/contacts";

      const resultHubspotContact = await fetch(urlHubspotFormContact, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: barierTokenHubspot,
        },
        body: JSON.stringify(bodyFormDataContact),
      })
        .then((res) => res.json())
        .then((json) => {
          return json;
        })
        .catch((error) => console.error("Error:", error));
      if (resultHubspotContact?.status === "error") {
        console.log(
          "ERROR HUBSPOT SUBMIT FORM" + JSON.stringify(resultHubspotContact)
        );
        const error: CustomErrorType = new Error("Failed to submit form");
        error.statusCode = 404;
        throw error;
      }

      //* SUBMIT POST PII IF FIRST TIME
      // Staging
      // const urlHubspotForm = "https://api.hsforms.com/submissions/v3/integration/secure/submit/22521314/dd6336e5-c6eb-474c-91ac-aa03e7ec72a0";
      // Production
      const urlHubspotForm =
        "https://api.hsforms.com/submissions/v3/integration/secure/submit/19533831/0c0a4f2b-1ad5-4a7d-a8ed-6fa031371fd8";

      const bodyFormDataPii = {
        fields: [
          {
            objectTypeId: "0-1",
            name: "email", //Required
            value: email || "",
          },
          {
            objectTypeId: "0-1",
            name: "template_type",
            value: template_type || "test", // Pass selected template type value here
          },
          {
            objectTypeId: "0-1",
            name: "image_url",
            value: image_url || "www.images/test/test-img01.com", //Pass uploaded photo image url
          },
          {
            objectTypeId: "0-1",
            name: "generating_image",
            value: generating_image || "www.images/test/test-img01.com", //Pass generated image url here
          },
        ],
        context: {
          hutk: hubspotutk || "2c1958564f0c0c53a6a438a4cec56cc6", //pass the cookie value of  “hubspotutk”
          pageUri: env.FRONT_END_URL, // "www.example.com", // static. Pass the url of the page where the form is submitted
          pageName: "OREO ", // static. Pass the name of the page here
        },
        legalConsentOptions: {
          consent: {
            consentToProcess: true,
            text: "I agree to allow Mondelez to store and process my personal data.", //if you  need, you can change this based on your microsite
            communications: [
              {
                value: true,
                subscriptionTypeId: 301937166,
                text: "I agree to receive marketing communications from Mondelez.", //if you  need, you can change this based on your microsite
              },
            ],
          },
        },
      };
      resultHubspotForm = await fetch(urlHubspotForm, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: barierTokenHubspot,
        },
        body: JSON.stringify(bodyFormDataPii),
      })
        .then((res) => res.json())
        .then((json) => {
          return json;
        })
        .catch((error) => console.error("Error:", error));
      if (resultHubspotForm?.status === "error") {
        console.log(
          "ERROR HUBSPOT SUBMIT FORM" + JSON.stringify(resultHubspotForm)
        );
        const error: CustomErrorType = new Error("Failed to submit form");
        error.statusCode = 404;
        throw error;
      }
    } else {
      //* SUBMIT CONTACT IS FOUND
      const urlHubspotFormContact = `https://api.hubapi.com/crm/v3/objects/contacts/${contactId}`;

      const resultHubspotContact = await fetch(urlHubspotFormContact, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: barierTokenHubspot,
        },
        body: JSON.stringify(bodyFormDataContact),
      })
        .then((res) => res.json())
        .then((json) => {
          return json;
        })
        .catch((error) => console.error("Error:", error));
      if (resultHubspotContact?.status === "error") {
        console.log(
          "ERROR HUBSPOT SUBMIT FORM" + JSON.stringify(resultHubspotContact)
        );
        const error: CustomErrorType = new Error("Failed to submit form");
        error.statusCode = 404;
        throw error;
      }

      //* SUBMIT POST PII IF ALREADY EXIST
      const urlHubspotForm = `https://api.hubapi.com/crm/v3/objects/contacts/${contactId}`;
      const bodyFormDataPii = {
        properties: {
          template_type: template_type || "test", //Choose a template of feel to eat
          image_url: image_url || "www.images/test/test-img01.com", //Upload Photo or Take Photo
          generating_image:
            generating_image || "www.images/test/test-img01.com", //Generating Image
        },
      };

      resultHubspotForm = await fetch(urlHubspotForm, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: barierTokenHubspot,
        },
        body: JSON.stringify(bodyFormDataPii),
      })
        .then((res) => res.json())
        .then((json) => {
          return json;
        })
        .catch((error) => console.error("Error:", error));
      if (resultHubspotForm?.status === "error") {
        console.log(
          "ERROR HUBSPOT SUBMIT FORM " + JSON.stringify(resultHubspotForm)
        );
        const error: CustomErrorType = new Error("Failed to submit form pii");
        error.statusCode = 404;
        throw error;
      }
    }

    if (data.typeOfSignup === "google") {
      dataForm["googleId"] = data.id;
    }
    if (data.typeOfSignup === "facebook") {
      dataForm["facebookId"] = data.id;
    }

    const urlSubmit = `${env.MBH_CMS_API_URL}/api/v1/public/hooks/participant-submission`;
    const result = await fetch(urlSubmit, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        api_key: api_key,
      },
      body: JSON.stringify(dataForm),
    })
      .then((res) => res.json())
      .then((json) => {
        return json;
      })
      .catch((error) => console.error("Error:", error));

    if (!result?.statusCode?.toString()?.startsWith("2")) {
      console.log("ERROR CMS SUBMIT FORM" + JSON.stringify(result));
      const error: CustomErrorType = new Error(
        result?.message || "Failed to submit form cms"
      );
      error.statusCode = 404;
      throw error;
    }

    console.log(
      "RESULT CMS SEARCHING PARTICIPANT : " + JSON.stringify(getParticipant)
    );
    console.log(
      "RESULT HUBSPOT SEARCHING CONTACT FORM :" +
        JSON.stringify(resSearchContact)
    );
    console.log(
      "RESULT HUBSPOT SUBMIT FORM : " + JSON.stringify(resultHubspotForm)
    );
    console.log("RESULT CMS SUBMIT FORM : " + JSON.stringify(result));

    return result;
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export default {
  submitForm,
};
